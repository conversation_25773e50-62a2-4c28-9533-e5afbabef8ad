<template>
  <div
    ref="dropdownRef"
    class="filter-select font-family-averta"
    :class="{
      'filter-select-expanded': isOpen,
    }"
  >
    <button
      ref="triggerRef"
      type="button"
      class="filter-select-toggle btn-reset font-size-base"
      @click="toggleDropdown"
    >
      <span class="font-bold color-slate-gray">{{ label }}</span>
      <span class="font-weight-extra-bold color-green-l">{{ selectedLabel }}</span>
      <img alt="arrow down green" src="@/assets/images/arrow-down-green.png" width="20" height="20" />

      <template v-if="defaultOptionValue !== appliedOptionValue">
        <span class="filter-select-toggle-hr"></span>
        <img
          class="filter-select-toggle-reset"
          alt="close green"
          src="@/assets/images/close-green.png"
          width="18"
          height="18"
          @click.stop="reset"
        />
      </template>
    </button>


    <Teleport to="body">
      <div
        v-if="isOpen"
        ref="dropdownEl"
        class="filter-select-dropdown"
        :style="dropdownStyle"
      >
        <div class="filter-select-dropdown-body">
          <div
            v-for="(option, index) in options"
            :key="option.value"
            class="filter-select-option"
            :class="{
              'filter-select-option-selected': selectedOptionValue === option.value,
            }"
          >
            <label :for="`filter-select-option-${option.value}-${index}`" class="control-radio w-100">
              <span class="font-size-base font-normal color-black">{{ option.label }}</span>
              <input
                type="radio"
                :id="`filter-select-option-${option.value}-${index}`"
                name="filter-select-option"
                :value="option.value"
                v-model="selectedOptionValue"
              >
              <span class="checkmark"></span>
            </label>
          </div>
        </div>
        <div class="filter-select-dropdown-actions">
          <button
            type="button"
            class="btn btn-green btn-small"
            :disabled="(selectedOptionValue === defaultOptionValue && appliedOptionValue === defaultOptionValue) || (selectedOptionValue === appliedOptionValue)"
            @click="applySelection"
          >
            {{ $t('BUTTONS.APPLY_BUTTON') }}
          </button>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { QUERY_PARAM_KEY } from "../../сonstants/query-param-key.js";

const props = defineProps({
  label: { type: String, default: '' },
  queryParam: { type: String, default: 'filter-select' },
  options: {
    type: Array,
    default: [], // [{label: '', value: '', hiddenValue: ''}]
  },
  defaultOption: { type: Object, default: {} },
});

const emit = defineEmits(['onAppliedOption']);

const router = useRouter();
const route = useRoute();

const isOpen = ref(false);
const defaultOptionValue = ref(props.defaultOption?.value || props.options[0].value);
const selectedOptionValue = ref(route.query?.[props.queryParam] || props.defaultOption?.value || props.options[0].value);
const appliedOptionValue = ref(route.query?.[props.queryParam] || props.defaultOption?.value || props.options[0].value);
const dropdownRef = ref(null);
const triggerRef = ref(null);
const dropdownEl = ref(null);
const dropdownStyle = ref({});

const selectedLabel = computed(() => props.options.find((opt) => opt.value === appliedOptionValue.value)?.label);

const reset = () => {
  selectedOptionValue.value = defaultOptionValue.value;
  applySelection();
};

const checkSelectedValue = () => {
  if (selectedOptionValue.value !== appliedOptionValue.value) {
    selectedOptionValue.value = appliedOptionValue.value || defaultOptionValue.value;
  }
};

const toggleDropdown = () => {
  isOpen.value = !isOpen.value;
  if (isOpen.value) {
    nextTick(() => {
      updateDropdownPosition();
      window.addEventListener('scroll', updateDropdownPosition, true);
      window.addEventListener('resize', updateDropdownPosition);
    });
  } else {
    checkSelectedValue();
    window.removeEventListener('scroll', updateDropdownPosition, true);
    window.removeEventListener('resize', updateDropdownPosition);
  }
};

const applySelection = () => {
  appliedOptionValue.value = selectedOptionValue.value;
  router.push({
    query: {
      ...route.query,
      [QUERY_PARAM_KEY.PAGE]: undefined,
      [props.queryParam]: selectedOptionValue.value,
    },
  })?.catch();
  isOpen.value = false;
  emit("onAppliedOption", selectedOptionValue.value);
  window.removeEventListener('scroll', updateDropdownPosition, true);
  window.removeEventListener('resize', updateDropdownPosition);
};

const updateDropdownPosition = () => {
  const trigger = triggerRef.value;
  const dropdown = dropdownEl.value;
  if (!trigger || !dropdown) return;

  const rect = trigger.getBoundingClientRect();
  const dropdownHeight = dropdown.offsetHeight;
  const dropdownWidth = Math.max(rect.width, 300);

  const spaceBelow = window.innerHeight - rect.bottom;
  const spaceAbove = rect.top;

  const fitsBelow = spaceBelow >= dropdownHeight;

  let top;
  if (fitsBelow || spaceBelow >= spaceAbove) {
    top = rect.bottom + 4;
  } else {
    top = rect.top - dropdownHeight - 4;
  }

  dropdownStyle.value = {
    position: 'absolute',
    top: '355px',
    left: `${Math.min(rect.left, window.innerWidth - dropdownWidth - 8)}px`,
    zIndex: 50,
    width: `${dropdownWidth}px`,
  };
};

const handleClickOutside = (e) => {
  if (
    !triggerRef.value?.contains(e.target) &&
    !dropdownEl.value?.contains(e.target)
  ) {
    isOpen.value = false;
    checkSelectedValue();
    window.removeEventListener('scroll', updateDropdownPosition, true);
    window.removeEventListener('resize', updateDropdownPosition);
  }
};

watch(() => route.query[props.queryParam], (val) => {
  if (val !== appliedOptionValue.value) {
    selectedOptionValue.value = val || defaultOptionValue.value;
    appliedOptionValue.value = val || defaultOptionValue.value;
  }
});

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
  window.removeEventListener('scroll', updateDropdownPosition, true);
  window.removeEventListener('resize', updateDropdownPosition);
});
</script>
