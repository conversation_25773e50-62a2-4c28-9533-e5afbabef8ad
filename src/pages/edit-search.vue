<template>
  <client-only>
    <content-wrapper
      class="padding-zero"
      :is-body-loading="isEditSearchLoading && !hasNoResultFound"
    >
      <div class="edit-search-main-container">
        <div class="main-edit-search">
          <div class="edit-search-header">
            <div class="right-side">
              <button
                type="button"
                v-show="!isEditSearchLoading && !hasNoResultFound"
                :class="
                  isCampaignModified
                    ? 'btn-green'
                    : 'btn-green disabled-button'
                "
                @click="publishData()"
                @keydown="preventEnterAndSpaceKeyPress($event)"
              >
                Publish
              </button>
            </div>
          </div>
          <div v-show="hasNoResultFound" class="result-not-found">
            <noResultFound />
          </div>
          <div
            class="filter-container-search"
            v-show="!isEditSearchLoading && !hasNoResultFound"
          >
            <div class="filter-container">
              <div class="filter-head text-h2">
                {{ $t("SEARCH_FILTER.CATEGORIES_FILTER") }}
              </div>
              <div class="filter-box">
                <div class="head-section">
                  <div class="left-side">
                    <span class="filter-name">{{
                      $t("CATEGORY.CATEGORY_TEXT")
                    }}</span>
                  </div>
                  <div class="right-side">
                    <button
                      type="button"
                      class="edit-button text-h3"
                      @click="isOpenCategories = !isOpenCategories"
                      @keydown="preventEnterAndSpaceKeyPress($event)"
                    >
                      <span v-if="!isOpenCategories">{{
                        $t("COMMON.VIEW")
                      }}</span>
                      <span v-else>{{ $t("COMMON.HIDE") }}</span>
                      <i
                        class="arrow-down-up"
                        :style="isOpenCategories ? transformIcon : ''"
                      ></i>
                    </button>
                  </div>
                </div>
                <div class="content-section" v-show="isOpenCategories">
                  <hr class="hr-tag" />
                  <div class="text-button-section">
                    <div class="text display-4">
                      Drag categories to position how they will appear in
                      category filter
                    </div>
                    <button
                      type="button"
                      :class="
                        !formattedCategoryPopUp ||
                        formattedCategory.length === addCategoriesTotal
                          ? 'add-category-button text-h3 disable'
                          : 'add-category-button text-h3'
                      "
                      @click="addCategoryButtonAsync()"
                      @keydown="preventEnterAndSpaceKeyPress($event)"
                    >
                      <img
                        alt=""
                        class="add-image"
                        src="@/assets/images/category-add.png"
                      />
                      {{ $t("CATEGORY.ADD_CATEGORY") }}
                    </button>
                  </div>
                  <draggable
                    v-if="formattedCategory?.length"
                    :list="formattedCategory"
                    class="list-group"
                    :scroll-sensitivity="200"
                    :force-fallback="true"
                    ghost-class="hidden-list"
                    @start="drag = true"
                    @end="drag = false"
                    @change="handleDrag"
                    v-model="formattedCategory"
                  >
                    <div
                      class="category-card"
                      v-for="(categories, index) in formattedCategory"
                      :key="index"
                    >
                      <div class="card-image">
                        <img
                          alt=""
                          class="image"
                          :src="
                            categories &&
                            categories.data &&
                            categories.data[lang]
                              ? categories.data[lang].image
                                ? categories.data[lang].image
                                : defaultImage
                              : defaultImage
                          "
                        />
                      </div>
                      <div
                        :class="{
                          'simple-data-tooltip': isCategoryNameTooltipVisible,
                        }"
                        :data-tooltip-text="isCategoryNameTooltipVisible && categories?.data?.[lang]?.name"
                      >
                        <div
                          class="card-title text-h3"
                          :id="`catFilter${index}`"
                          @mouseover="call(index)"
                          @mouseleave="hide(index)"
                          @mousedown="hide(index)"
                        >
                          {{ categories?.data?.[lang]?.name ?? "" }}
                        </div>
                      </div>
                      <div class="total-recipe">
                        <span v-if="categories.totalRecipes > 1"
                          >{{ categories.totalRecipes }} recipes</span
                        >
                        <span v-if="categories.totalRecipes <= 1"
                          >{{ categories.totalRecipes }} recipe</span
                        >
                      </div>
                      <div class="delete-icon">
                        <img
                          alt=""
                          src="@/assets/images/white-delete.png"
                          @click="deleteCategory(categories, index)"
                          width="18"
                          height="20"
                        />
                      </div>
                    </div>
                  </draggable>
                </div>
              </div>
            </div>
          </div>
          <div
            class="filter-container-other-search"
            v-show="!isEditSearchLoading && !hasNoResultFound"
          >
            <div class="filter-container">
              <div class="filter-head">
                <div class="filter-tag-head text-h2">
                  {{ $t("SEARCH_FILTER.OTHER_FILTERS") }}
                </div>
                <button
                  type="button"
                  @click="displayCreateTagModal()"
                  class="btn-green"
                >
                  {{ $t("CATEGORY_GROUP.NEW_GROUP") }}
                </button>
              </div>
              <draggable
                v-if="formattedFilter?.length"
                :list="formattedFilter"
                class="list-group"
                ghost-class="hidden-list"
                :scroll-sensitivity="200"
                :force-fallback="true"
                @start="drag = true"
                @end="drag = false"
                handle=".draggable-icon"
                @change="handleDrag"
                v-model="formattedFilter"
              >
                <div
                  class="filter-box"
                  v-for="(tag, index) in formattedFilter"
                  :key="index"
                >
                  <div
                    :class="
                      tag.dropDown ? 'head-section border-hide' : 'head-section'
                    "
                  >
                    <img
                      alt=""
                      class="draggable-icon"
                      src="@/assets/images/drag-vertically.svg?skipsvgo=true"
                    />
                    <div class="left-side">
                      <div
                        v-if="tag && tag.type && tag.type == 'tags'"
                        class="filter-icon-tags"
                      >
                        <img
                          alt=""
                          class="filter-image"
                          src="@/assets/images/tag_icon.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-if="tag && tag.type && tag.type == 'diets'"
                        class="filter-icon-diets"
                      >
                        <img
                          alt=""
                          class="filter-image"
                          src="@/assets/images/diet_icon.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        :class="{
                          'simple-data-tooltip': isTagNameTooltipVisible,
                        }"
                        :data-tooltip-text="isTagNameTooltipVisible && tag?.name"
                      >
                        <span
                          class="filter-name"
                          :id="`tagname${index}`"
                          @mouseover="checkIngtagName(tag, index)"
                          @mouseleave="hideTagNameTip(index)"
                          >{{ tag?.name ?? "" }}</span
                        >
                      </div>
                    </div>
                    <div class="right-side">
                      <div
                        class="edit-btn"
                        @click="tagRenamePopup(tag, index, tag.name, tag.type)"
                      >
                        <img
                          alt=""
                          src="@/assets/images/edit_icon_black.png"
                        />
                      </div>
                      <div
                        class="edit-btn"
                        @click="deleteTagGroupPopUp(tag, index)"
                      >
                        <img
                          alt=""
                          src="~/assets/images/delete-icon.png"
                        />
                      </div>
                      <button
                        type="button"
                        class="edit-button text-h3"
                        @click="tagDropdown(tag)"
                        @keydown="preventEnterAndSpaceKeyPress($event)"
                      >
                        <span v-if="tag.dropDown">{{ $t("COMMON.HIDE") }}</span>
                        <span v-else>{{ $t("COMMON.VIEW") }}</span>
                        <i
                          class="arrow-down-up"
                          :style="tag.dropDown ? transformIcon : ''"
                        ></i>
                      </button>
                    </div>
                  </div>
                  <div class="content-section-other" v-show="tag.dropDown">
                    <hr class="hr-tag" />
                    <div class="text-button-section">
                      <div v-if="tag.type == 'tags'" class="text display-4">
                        Drag tags to reorder
                      </div>
                      <div v-if="tag.type == 'diets'" class="text display-4">
                        Drag diets to reorder
                      </div>
                      <button
                        type="button"
                        class="add-category-button text-h3"
                        :class="{
                          'add-category-button':
                            emptyTagCheck && emptyTagCheck.length,
                          disable: tag.values.length === tagCount,
                        }"
                        @click="openTagListModalAsync(false, tag.name)"
                        @keydown="preventEnterAndSpaceKeyPress($event)"
                        v-if="tag.type === 'tags'"
                      >
                        <img
                          alt=""
                          class="add-image"
                          src="@/assets/images/category-add.png"
                        />
                        {{ $t("COMMON.ADD_TAG") }}
                      </button>
                      <button
                        type="button"
                        class="add-category-button text-h3"
                        :class="{
                          'add-category-button':
                            emptyDietCheck && emptyDietCheck.length,
                          disable: tag.values.length === dietCount,
                        }"
                        @click="openDietListModalAsync(false, tag.name)"
                        @keydown="preventEnterAndSpaceKeyPress($event)"
                        v-if="tag.type === 'diets'"
                      >
                        <img
                          alt=""
                          class="add-image"
                          src="@/assets/images/category-add.png"
                        />
                        {{ $t("COMMON.ADD_DIET") }}
                      </button>
                    </div>
                    <draggable
                      v-if="tag.values?.length"
                      :list="tag.values"
                      class="list-group"
                      ghost-class="hidden-list"
                      :scroll-sensitivity="200"
                      :force-fallback="true"
                      @start="drag = true"
                      @end="drag = false"
                      @change="handleDrag"
                      v-model="tag.values"
                    >
                      <div
                        class="tag-card"
                        v-for="(tagDetail, indexValue) in tag.values"
                        :key="indexValue"
                      >
                        <div v-if="tag.type == 'tags'">
                          <img
                            alt=""
                            class="black-tag-image"
                            src="@/assets/images/tags-black-icon.svg?skipsvgo=true"
                          />
                          <img
                            alt=""
                            class="white-tag-image"
                            src="@/assets/images/tags-icon.svg?skipsvgo=true"
                          />
                          <div
                            :class="{
                              'simple-data-tooltip': isTagDetailNameTooltipVisible,
                            }"
                            :data-tooltip-text="isTagDetailNameTooltipVisible && tagDetail?.data?.[lang]?.name"
                          >
                            <div
                              class="card-title text-h3"
                              @mouseover="checkFilterTagName(indexValue, index)"
                              @mouseleave="
                                hideFilterTagNameTip(indexValue, index)
                              "
                              @mousedown="hideFilterTagNameTip(indexValue, index)"
                              :id="`checkname${index}${indexValue}`"
                            >
                              {{ tagDetail?.data?.[lang]?.name ?? "" }}
                            </div>
                          </div>
                          <div class="total-recipe">
                            <span v-if="tagDetail.totalRecipes > 1"
                              >{{ tagDetail.totalRecipes }} recipes</span
                            >
                            <span v-if="tagDetail.totalRecipes <= 1"
                              >{{ tagDetail.totalRecipes }} recipe</span
                            >
                          </div>
                          <div class="delete-icon">
                            <img
                              alt=""
                              src="@/assets/images/white-delete.png"
                              @click="
                                deleteTagDiet(tagDetail, tag.name, 'tags')
                              "
                              width="18"
                              height="20"
                            />
                          </div>
                        </div>
                        <div v-if="tag.type == 'diets'">
                          <img
                            alt=""
                            class="diet-image"
                            :src="
                              tagDetail && tagDetail.image
                                ? tagDetail.image
                                : ''
                            "
                          />
                          <div
                            :class="{
                              'simple-data-tooltip': isTagNameLangTooltipVisible,
                            }"
                            :data-tooltip-text="isTagNameLangTooltipVisible && tagDetail?.name"
                          >
                            <div
                              :id="`diet${index}${indexValue}`"
                              class="card-title text-h3"
                              @mouseover="checkDietName(indexValue, index)"
                              @mouseleave="hideDietNameTip(indexValue, index)"
                              @mousedown="hideDietNameTip(indexValue, index)"
                            >
                              {{ tagDetail?.name ?? "" }}
                            </div>
                          </div>
                          <div class="total-recipe">
                            <span v-if="tagDetail.totalRecipes > 1"
                              >{{ tagDetail.totalRecipes }} recipes</span
                            >
                            <span v-if="tagDetail.totalRecipes <= 1"
                              >{{ tagDetail.totalRecipes }} recipe</span
                            >
                          </div>
                          <div class="delete-icon">
                            <img
                              alt=""
                              src="@/assets/images/white-delete.png"
                              @click="
                                deleteTagDiet(tagDetail, tag.name, 'diets')
                              "
                              width="18"
                              height="20"
                            />
                          </div>
                        </div>
                      </div>
                    </draggable>
                    <div class="delete-flex">
                      <div
                        class="delete-btn"
                        @click="deleteTagGroupPopUp(tag, index)"
                      >
                        <img
                          alt=""
                          class="image"
                          src="~/assets/images/delete-icon.png"
                        />
                        <span class="text text-h3">{{
                          $t("COMMON.DELETE_GROUP")
                        }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </draggable>
            </div>
          </div>
          <Modal
            v-show="isGroupModalVisible"
            @close="closeModal"
            id="problemModal"
          >
            <template #selectGroupType>
              <div class="select-group-modal">
                <div class="select-group-modal-content">
                  <div class="select-group-modal-heading display-3">
                    Please select a type of Group
                  </div>
                  <div class="select-group-modal-checkbox">
                    <div
                      v-for="(groupType, index) in selectGroupTypes"
                      :key="index"
                    >
                      <label class="control-radio display-4">
                        <input
                          type="radio"
                          :id="groupType.key"
                          @change="selectGroup(groupType)"
                          :value="groupType.key"
                          v-model="selectedGroupType"
                        />
                        <span class="checkmark"></span>
                        <p>{{ groupType.options }}</p>
                      </label>
                      <br />
                    </div>
                  </div>
                  <div class="select-group-modal-btn-container">
                    <button
                      type="button"
                      class="btn-green-outline"
                      @click="closeModal()"
                    >
                      {{ $t("BUTTONS.CANCEL_BUTTON") }}
                    </button>
                    <button
                      type="button"
                      class="btn-green"
                      @click="selectedGroupOption()"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            </template>
          </Modal>
          <Modal
            v-show="isAddCategoryModal"
            @close="closeModal"
            id="isAddCategoriesGroupModal"
          >
            <template #isAddCategoriesGroupModal>
              <div class="add-category-modal">
                <div class="title-section">
                  <img
                    alt=""
                    class="exit"
                    src="@/assets/images/exit-gray.png"
                    @click="closeModal()"
                  />
                  <div class="title">Add Categories</div>
                </div>
                <div class="search-container-for-category">
                  <div class="search-title">
                    Select Categories to add to the filter
                  </div>
                  <div class="search-box">
                    <button
                      type="button"
                      class="search-icon btn-reset"
                      @click="getCategorySearchAsync()"
                    >
                      <img
                        alt="search-icon"
                        :class="
                          queryCategory
                            ? 'search-icon-green-image'
                            : 'search-icon-green-image disabled-search-icon'
                        "
                        src="@/assets/images/search-grey.png"
                      />
                    </button>
                    <input
                      type="text"
                      class="search-input-box"
                      autocomplete="off"
                      placeholder="Find a Category"
                      v-model="queryCategory"
                      @keypress.enter="getCategorySearchAsync()"
                      :class="{ 'align-search-input-box': queryCategory }"
                    />
                    <button
                      type="button"
                      class="exit-icon btn-reset"
                      v-if="isSearchExitEnable"
                      @click="resetQueryAsync()"
                    >
                      <img
                        alt="exit-icon"
                        class="exit-search-icon"
                        src="@/assets/images/exit-search.svg?skipsvgo=true"
                      />
                    </button>
                  </div>
                </div>
                <div class="add-group-content" id="addGroupContent">
                  <div v-if="isTableDataLoading" class="table-image-loader">
                    <div class="loader"></div>
                  </div>
                  <div class="container" v-if="!isTableDataLoading">
                    <div
                      v-if="
                        categoryUpdateList == '' &&
                        isDisplayNoRecipeSection &&
                        isSearchExitEnable
                      "
                      class="no-recipe-result"
                    >
                      {{ $t("COMMON.NO_RESULTS") }}
                    </div>
                    <div
                      class="card"
                      v-for="(categories, index) in formattedCategoryPopUp"
                      :key="index"
                      :class="
                        (categories.isChecked ? 'selected-categories' : '') ||
                        (categories.isAlreadyAddedCategory
                          ? 'already-added-category'
                          : '')
                      "
                      @click="isChecked(categories)"
                    >
                      <div class="card-image">
                        <img
                          alt=""
                          class="image"
                          :src="
                            categories &&
                            categories.data &&
                            categories.data[lang] &&
                            categories.data[lang].image
                              ? categories.data[lang].image
                              : defaultImage
                          "
                        />
                      </div>
                      <div class="card-title text-h3">
                        {{
                          categories &&
                          categories.data &&
                          categories.data[lang] &&
                          categories.data[lang].name
                            ? categories.data[lang].name
                            : ""
                        }}
                      </div>
                      <div class="total-recipe">
                        <span v-if="categories.totalRecipes > 1"
                          >{{ categories.totalRecipes }} recipes</span
                        >
                        <span v-if="categories.totalRecipes <= 1"
                          >{{ categories.totalRecipes }} recipe</span
                        >
                      </div>
                      <span class="checkmark"></span>
                    </div>
                  </div>
                  <div class="load-button" v-if="hasLoadMoreItems">
                    <button
                      type="button"
                      class="btn-green"
                      @click="loadUpdatePopUpAsync()"
                      @keydown="preventEnterAndSpaceKeyPress($event)"
                    >
                      {{ $t("COMMON.LOAD_MORE") }}
                    </button>
                  </div>
                </div>
                <div class="add-group-content-space"></div>
                <div class="create-section">
                  <div class="count-selected">
                    Categories Selected: <b>{{ countCategoriesSelected }}</b>
                  </div>
                  <button
                    type="button"
                    class="btn-green create-btn"
                    @click="addCategories()"
                    @keydown="preventEnterAndSpaceKeyPress($event)"
                    :class="
                      countCategoriesSelected === 0
                        ? 'disabled-button'
                        : ''
                    "
                  >
                    {{ $t("COMMON.ADD") }}
                  </button>
                </div>
              </div>
            </template>
          </Modal>
          <deleteModal
            v-if="isDeleteCategoryModal"
            :closeModal="closeModal"
            :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_CATEGORY')"
            :productDescriptionOne="
              $t('DESCRIPTION_POPUP.REMOVE_CATEGORY_POPUP')
            "
            :productDescriptionTwo="'categories filter?'"
            :deleteItem="deleteCategoryBtn"
            :availableLanguage="0"
            :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
          />
          <deleteModal
            v-if="isDeleteTagModal"
            :closeModal="closeModal"
            :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_TAG')"
            :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_TAG_POPUP')"
            :productDescriptionTwo="'tag group?'"
            :deleteItem="deleteTagDietBtn"
            :availableLanguage="0"
            :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
          />
          <deleteModal
            v-if="isDeleteDietModal"
            :closeModal="closeModal"
            :productInfoTitle="'Remove Diet?'"
            :productDescriptionOne="'Are you sure you want to remove this diet from the'"
            :productDescriptionTwo="'diet group?'"
            :deleteItem="deleteTagDietBtn"
            :availableLanguage="0"
            :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
          />
          <deleteModal
            v-if="isTagGroupDeletePopup"
            :closeModal="closeModal"
            :productInfoTitle="'Delete Tag Group?'"
            :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
            :productDescriptionTwo="'Group?'"
            :deleteItem="deleteTagGroupBtn"
            :availableLanguage="0"
            :buttonText="$t('BUTTONS.DELETE_BUTTON')"
          />
          <deleteModal
            v-if="isDietGroupDeletePopup"
            :closeModal="closeModal"
            :productInfoTitle="'Delete Diet Group?'"
            :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
            :productDescriptionTwo="'Group?'"
            :deleteItem="deleteTagGroupBtn"
            :availableLanguage="0"
            :buttonText="$t('BUTTONS.DELETE_BUTTON')"
          />
          <cancelModal
            v-if="isConfirmModalVisible"
            :availableLang="[]"
            :isCampaignModifiedFromShoppableReview="false"
            :callConfirm="closeSearch"
            :closeModal="closeModal"
          />
          <Modal v-show="isDisplayCreateTagModal" @close="closeModal">
            <template #createCtaegoryGroup>
              <div class="create-tag-group">
                <img
                  alt=""
                  class="back"
                  src="@/assets/images/arrow-gray.png"
                  @click="backToSelectType('tags')"
                />
                <div class="close-image-icon">
                  <img
                    alt=""
                    @click="closeModal"
                    src="~/assets/images/exit-gray.png"
                  />
                </div>
                <div class="folder-image-icon">
                  <img alt="" src="~/assets/images/folder.png" />
                </div>
                <div class="create-tag-group-modal">
                  <div class="create-tag-heading">
                    Create a name for your Tag Group.
                  </div>
                  <div class="create-tag-desc">
                    A Tag Group is a collection of tags.
                  </div>
                  <div class="create-tag-group-button">
                    <input
                      type="text"
                      autocomplete="off"
                      class="create-tag-group-name"
                      v-model="groupName"
                      placeholder="Your Group Name"
                      @keypress.enter="groupNameEnter('tags')"
                      @input="inputChecked('tags')"
                    />
                    <img
                      alt=""
                      v-if="!isValidInputlink"
                      class="error-image"
                      src="~/assets/images/red-info.svg?skipsvgo=true"
                    />
                    <span v-if="!isValidInputlink" class="error-message"
                      >This name already exists.</span
                    >
                  </div>
                  <div class="create-tag-group-button-container">
                    <button
                      type="button"
                      class="btn-green"
                      :class="{
                        'disabled-button':
                          isAddButtonValidInput || groupName.trim() == '',
                      }"
                      @click="openTagListModalAsync(true, '')"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            </template>
          </Modal>
          <Modal v-show="isDisplayCreateDietModal" @close="closeModal">
            <template #createCtaegoryGroup>
              <div class="create-tag-group">
                <img
                  alt=""
                  class="back"
                  src="@/assets/images/arrow-gray.png"
                  @click="backToSelectType('diets')"
                />
                <div class="close-image-icon">
                  <img
                    alt=""
                    @click="closeModal"
                    src="~/assets/images/exit-gray.png"
                  />
                </div>
                <div class="folder-image-icon">
                  <img
                    alt=""
                    src="~/assets/images/diet_create_icon.svg?skipsvgo=true"
                  />
                </div>
                <div class="create-tag-group-modal">
                  <div class="create-tag-heading">
                    Create a name for your Diet Group.
                  </div>
                  <div class="create-tag-desc">
                    A Diet Group is a collection of diets.
                  </div>
                  <div class="create-tag-group-button">
                    <input
                      type="text"
                      autocomplete="off"
                      class="create-tag-group-name"
                      v-model="groupName"
                      placeholder="Your Group Name"
                      @keypress.enter="groupNameEnter('diets')"
                      @input="inputChecked('diets')"
                    />
                    <img
                      alt=""
                      v-if="!isValidInputlink"
                      class="error-image"
                      src="~/assets/images/red-info.svg?skipsvgo=true"
                    />
                    <span v-if="!isValidInputlink" class="error-message"
                      >This name already exists.</span
                    >
                  </div>
                  <div class="create-tag-group-button-container">
                    <button
                      type="button"
                      class="btn-green"
                      :class="{
                        'disabled-button':
                          isAddButtonValidInput || groupName.trim() == '',
                      }"
                      @click="openDietListModalAsync(true, '')"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            </template>
          </Modal>
          <Modal v-show="isRenameTagModal" @close="closeModal">
            <template #createCtaegoryGroup>
              <div class="create-tag-group">
                <div class="close-image-icon">
                  <img
                    alt=""
                    @click="closeModal"
                    src="~/assets/images/exit-gray.png"
                  />
                </div>
                <div class="folder-image-icon">
                  <img alt="" src="~/assets/images/folder.png" />
                </div>
                <div class="create-tag-group-modal">
                  <div class="create-tag-heading rename-tag-heading">
                    Rename your Tag Group.
                  </div>
                  <div class="create-tag-desc">
                    A Tag Group is a collection of tags.
                  </div>
                  <div class="create-tag-group-button">
                    <input
                      type="text"
                      autocomplete="off"
                      class="create-tag-group-name"
                      v-model="renameGroupName"
                      placeholder="Your Group Name"
                      @keypress.enter="groupRenameEnter()"
                      @input="inputRenameChecked('tags')"
                    />
                    <img
                      alt=""
                      v-if="!isValidInputlink"
                      class="error-image"
                      src="~/assets/images/red-info.svg?skipsvgo=true"
                    />
                    <img
                      alt=""
                      v-if="
                        !isAddButtonValidInput && renameGroupName.trim() != ''
                      "
                      class="check-image"
                      src="~/assets/images/green-check.png"
                    />
                    <span v-if="!isValidInputlink" class="error-message"
                      >This name already exists.</span
                    >
                  </div>
                  <div class="create-tag-group-button-container">
                    <button
                      type="button"
                      class="btn-green"
                      :class="{
                        'disabled-button':
                          isAddButtonValidInput ||
                          renameGroupName.trim() == '' ||
                          Orignalname == renameGroupName,
                      }"
                      @click="groupRenameEnter()"
                    >
                      {{ $t("BUTTONS.SAVE_BUTTON") }}
                    </button>
                  </div>
                </div>
              </div>
            </template>
          </Modal>
          <Modal v-show="isRenameDietModal" @close="closeModal">
            <template #createCtaegoryGroup>
              <div class="create-tag-group">
                <div class="close-image-icon">
                  <img
                    alt=""
                    @click="closeModal"
                    src="~/assets/images/exit-gray.png"
                  />
                </div>
                <div class="folder-image-icon">
                  <img
                    alt=""
                    src="~/assets/images/diet_create_icon.svg?skipsvgo=true"
                  />
                </div>
                <div class="create-tag-group-modal">
                  <div class="create-tag-heading rename-tag-heading">
                    Rename your Diet Group.
                  </div>
                  <div class="create-tag-desc">
                    A Diet Group is a collection of diets.
                  </div>
                  <div class="create-tag-group-button">
                    <input
                      type="text"
                      autocomplete="off"
                      class="create-tag-group-name"
                      v-model="renameGroupName"
                      placeholder="Your Group Name"
                      @keypress.enter="groupDietRenameEnter()"
                      @input="inputRenameChecked('diets')"
                    />
                    <img
                      alt=""
                      v-if="!isValidInputlink"
                      class="error-image"
                      src="~/assets/images/red-info.svg?skipsvgo=true"
                    />
                    <img
                      alt=""
                      v-if="
                        !isAddButtonValidInput && renameGroupName.trim() != ''
                      "
                      class="check-image"
                      src="~/assets/images/green-check.png"
                    />
                    <span v-if="!isValidInputlink" class="error-message"
                      >This name already exists.</span
                    >
                  </div>
                  <div class="create-tag-group-button-container">
                    <button
                      type="button"
                      class="btn-green"
                      :class="{
                        'disabled-button':
                          isAddButtonValidInput ||
                          renameGroupName.trim() == '' ||
                          OrignalDietname == renameGroupName,
                      }"
                      @click="groupDietRenameEnter()"
                    >
                      {{ $t("BUTTONS.SAVE_BUTTON") }}
                    </button>
                  </div>
                </div>
              </div>
            </template>
          </Modal>
          <Modal
            v-if="isAddTagModal || isAddDietModal"
            @close="closeModal"
            id="isAddCategoriesGroupModal"
          >
            <template #isAddCategoriesGroupModal>
              <div class="add-tag-modal">
                <div class="title-section">
                  <button
                    type="button"
                    class="btn-reset"
                    v-if="!isNonGroup"
                    @click="
                      backGroupModalAsync(
                        isAddTagModal ? 'tags' : isAddDietModal ? 'diets' : null
                      )
                    "
                  >
                    <img
                      alt="back-icon"
                      class="back"
                      src="@/assets/images/arrow-gray.png"
                    />
                  </button>
                  <img
                    alt=""
                    class="exit"
                    src="@/assets/images/exit-gray.png"
                    @click="closeModal()"
                  />
                  <div v-show="!isNonGroup && isAddTagModal" class="title">
                    Add Tags to {{ groupName }}
                  </div>
                  <div
                    v-show="isNonGroup && groupTitleName && isAddTagModal"
                    class="title"
                  >
                    Add Tags to {{ groupTitleName }}
                  </div>
                  <div v-show="!isNonGroup && isAddDietModal" class="title">
                    Add Diets to {{ groupName }}
                  </div>
                  <div
                    v-show="isNonGroup && groupTitleName && isAddDietModal"
                    class="title"
                  >
                    Add Diets to {{ groupTitleName }}
                  </div>
                </div>
                <div class="search-container">
                  <div v-if="isAddTagModal" class="search-title">
                    Select tags to add to your group.
                  </div>
                  <div v-if="isAddDietModal" class="search-title">
                    Select diets to add to your group.
                  </div>
                  <div class="search-box-tag">
                    <button
                      type="button"
                      class="btn-reset"
                      @click="
                        isAddDietModal
                          ? getDietSearchAsync()
                          : getTagSearchAsync()
                      "
                      v-if="isAddDietModal || isAddTagModal"
                    >
                      <img
                        alt="search-icon"
                        class="search-icon-green-image"
                        :class="
                          !queryDiet || !queryTag ? 'disabled-search-icon' : ''
                        "
                        src="@/assets/images/search-grey.png"
                      />
                    </button>
                    <input
                      v-if="isAddTagModal"
                      type="text"
                      class="search-input-box"
                      autocomplete="off"
                      placeholder="Find a tag"
                      v-model="queryTag"
                      @keypress.enter="getTagSearchAsync()"
                      :class="{ 'align-search-input-box': queryTag }"
                    />
                    <input
                      v-if="isAddDietModal"
                      type="text"
                      class="search-input-box"
                      autocomplete="off"
                      placeholder="Find a diet"
                      v-model="queryDiet"
                      @keypress.enter="getDietSearchAsync()"
                      :class="{ 'align-search-input-box': queryDiet }"
                    />
                    <button
                      type="button"
                      class="btn-reset"
                      v-if="
                        isSearchExitOtherEnable &&
                        ((isAddDietModal && queryDiet) ||
                          (isAddTagModal && queryTag))
                      "
                      @click="
                        queryDiet ? resetDietQueryAsync() : resetTagQueryAsync()
                      "
                    >
                      <img
                        alt="exit-icon"
                        class="exit-search-icon"
                        src="@/assets/images/exit-search.svg?skipsvgo=true"
                      />
                    </button>
                  </div>
                </div>
                <div
                  v-if="isAddTagModal"
                  class="add-group-content"
                  id="addGroupContent"
                >
                  <div v-if="isTableDataLoading" class="table-image-loader">
                    <div class="loader"></div>
                  </div>
                  <div class="container tag-popup" v-if="!isTableDataLoading">
                    <div
                      v-if="
                        tagUpdateList &&
                        tagUpdateList.length === 0 &&
                        isDisplayNoRecipeSection &&
                        isSearchExitOtherEnable
                      "
                      class="no-recipe-result"
                    >
                      {{ $t("COMMON.NO_RESULTS") }}
                    </div>
                    <div
                      v-if="
                        tagUpdateList &&
                        tagUpdateList.length === 0 &&
                        !isSearchExitOtherEnable &&
                        isNoResultSectionVisible
                      "
                      class="no-recipe-result"
                    >
                      {{ $t("COMMON.NO_RESULTS") }}
                    </div>
                    <div
                      class="tag-card"
                      v-for="(tags, index) in formatedTagPopUp"
                      :key="index"
                      :class="
                        (tags.isChecked ? 'selected-tags' : '') ||
                        (tags.isAlreadyAddedTag ? 'already-added-category' : '')
                      "
                      @click="isCheckedTag(tags)"
                    >
                      <img
                        alt=""
                        class="black-tag-image"
                        src="@/assets/images/tags-black-icon.svg?skipsvgo=true"
                      />
                      <img
                        alt=""
                        class="white-tag-image"
                        src="@/assets/images/tags-icon.svg?skipsvgo=true"
                      />
                      <div class="card-title text-h3">
                        {{
                          tags &&
                          tags.data &&
                          tags.data[lang] &&
                          tags.data[lang].name
                            ? tags.data[lang].name
                            : ""
                        }}
                      </div>
                      <div class="total-recipe">
                        <span v-if="tags.totalRecipes > 1"
                          >{{ tags.totalRecipes }} recipes</span
                        >
                        <span v-if="tags.totalRecipes <= 1"
                          >{{ tags.totalRecipes }} recipe</span
                        >
                      </div>
                      <span class="checkmark"></span>
                    </div>
                  </div>
                  <div
                    class="load-button"
                    v-if="fromPopUpTag + sizePopUpTag < addTagTotal"
                  >
                    <button
                      type="button"
                      class="btn-green"
                      @click="loadUpdateTagPopUpAsync()"
                      @keydown="preventEnterAndSpaceKeyPress($event)"
                    >
                      {{ $t("COMMON.LOAD_MORE") }}
                    </button>
                  </div>
                </div>
                <div
                  v-if="isAddDietModal"
                  class="add-group-content"
                  id="addGroupContent"
                >
                  <div v-if="isTableDataLoading" class="table-image-loader">
                    <div class="loader"></div>
                  </div>
                  <div class="container diet-popup" v-if="!isTableDataLoading">
                    <div
                      v-if="
                        dietPopupList &&
                        dietPopupList.length == 0 &&
                        isDisplayNoRecipeSection &&
                        isSearchExitOtherEnable
                      "
                      class="no-recipe-result"
                    >
                      {{ $t("COMMON.NO_RESULTS") }}
                    </div>
                    <div
                      class="tag-card"
                      v-for="(diets, index) in dietPopupList"
                      :key="index"
                      :class="
                        (diets.isChecked ? 'selected-tags' : '') ||
                        (diets.isAlreadyAddedDiet
                          ? 'already-added-category'
                          : '')
                      "
                      @click="isCheckedDiet(diets)"
                    >
                      <img
                        alt=""
                        class="black-tag-image"
                        :src="diets && diets.image ? diets.image : ''"
                      />
                      <img
                        alt=""
                        class="white-tag-image"
                        src="@/assets/images/tags-icon.svg?skipsvgo=true"
                      />
                      <div class="card-title text-h3">
                        {{ diets.name }}
                      </div>
                      <div class="total-recipe">
                        <span v-if="diets.totalRecipes > 1"
                          >{{ diets.totalRecipes }} recipes</span
                        >
                        <span v-if="diets.totalRecipes <= 1"
                          >{{ diets.totalRecipes }} recipe</span
                        >
                      </div>
                      <span class="checkmark"></span>
                    </div>
                  </div>
                </div>
                <div class="add-group-content-space"></div>
                <div class="create-section">
                  <div v-if="isAddTagModal" class="count-selected">
                    Tags Selected: <b>{{ countTagSelected }}</b>
                  </div>
                  <div v-if="isAddDietModal" class="count-selected">
                    Diets Selected: <b>{{ countDietSelected }}</b>
                  </div>
                  <button
                    type="button"
                    v-if="isNonGroup && isAddTagModal"
                    class="btn-green create-btn"
                    @click="addTagToGroup()"
                    @keydown="preventEnterAndSpaceKeyPress($event)"
                    :class="
                      countTagSelected === 0
                        ? 'disabled-button'
                        : ''
                    "
                  >
                    {{ $t("COMMON.ADD") }}
                  </button>
                  <button
                    type="button"
                    v-if="isNonGroup && isAddDietModal"
                    class="btn-green create-btn"
                    @click="addDietToGroup()"
                    @keydown="preventEnterAndSpaceKeyPress($event)"
                    :class="
                      countDietSelected === 0
                        ? 'disabled-button'
                        : ''
                    "
                  >
                    {{ $t("COMMON.ADD") }}
                  </button>
                  <button
                    type="button"
                    v-if="!isNonGroup && isAddTagModal"
                    class="btn-green create-btn"
                    @click="createTagGroup()"
                    @keydown="preventEnterAndSpaceKeyPress($event)"
                    :class="
                      countTagSelected === 0
                        ? 'disabled-button'
                        : ''
                    "
                  >
                    Create
                  </button>
                  <button
                    type="button"
                    v-if="!isNonGroup && isAddDietModal"
                    class="btn-green create-btn"
                    @click="createDietGroup()"
                    @keydown="preventEnterAndSpaceKeyPress($event)"
                    :class="
                      countDietSelected === 0
                        ? 'disabled-button'
                        : ''
                    "
                  >
                    Create
                  </button>
                </div>
              </div>
            </template>
          </Modal>
          <Modal v-show="isErrorOccuredModal" @close="closeModal">
            <template #nutrition>
              <div class="edit-product-publish-modal">
                <div class="publish-content">
                  <div class="publish-head display-4">
                    <div v-if="isunableToSaveFilter">
                      <span class="unable-to-save">
                        Unable to Save & Publish the Filter
                      </span>
                      <br />
                      <span class="unable-to-save-title">
                        The following field(s) cannot be empty:
                      </span>
                      <br />
                      <div
                        v-if="isunableToSaveFilter"
                        class="unable-to-save-list"
                      >
                        Filter Groups
                      </div>
                    </div>
                    <span v-if="!isunableToSaveFilter"
                      >Unable to Save & Publish the Filter, an unexpected error
                      occurred</span
                    >
                  </div>
                  <div class="button-container">
                    <button
                      type="button"
                      class="btn-green"
                      @click="closeModal"
                    >
                      Okay
                    </button>
                  </div>
                </div>
              </div>
            </template>
          </Modal>
          <savingModal v-show="isFilterPublishing" :status="'publishing'" />
          <saveModal
            v-if="hasPublish"
            :closeModal="closeModal"
            :saveAndPublishFunction="savePublishSearch"
            :availableLang="[]"
            :buttonName="$t('BUTTONS.PUBLISH_BUTTON')"
            :description="$t('DESCRIPTION_POPUP.PUBLISH_UPDATES_POPUP')"
            :imageName="publishIcon"
          />
        </div>
      </div>
    </content-wrapper>
  </client-only>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, getCurrentInstance, watchEffect } from "vue";
import { useStore } from "vuex";
import ContentWrapper from "@/components/content-wrapper/content-wrapper";
import savingModal from "@/components/saving-modal";
import cancelModal from "@/components/cancel-modal";
import saveModal from "@/components/save-modal.vue";
import Modal from "@/components/Modal";
import deleteModal from "@/components/delete-modal";
import NoResultFound from "../components/no-result-found.vue";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { useNuxtApp } from "#app";
import { useProjectLang } from "@/composables/useProjectLang";
import { useEventUtils } from "@/composables/useEventUtils";

// composable functions
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useRefUtils } from "~/composables/useRefUtils";

// images
import publishIcon from "~/assets/images/publish-variant-icon.png"

const { preventEnterAndSpaceKeyPress } = useEventUtils()



// Store
const store = useStore();
const router = useRouter();


const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { $eventBus, $tracker, $auth } = useNuxtApp();
const { t } = useI18n();
const { triggerLoading } = useCommonUtils();
const { getRef } = useRefUtils();
const { readyProject } = useProjectLang();

// Reactive references
const project = ref({});
const isFilterPublishing = ref(false);
const dietPopupList = ref([]);
const isGroupModalVisible = ref(false);
const isTableDataLoading = ref(false);
const defaultImage = import("~/assets/images/default_recipe_image.png");
const isOpenCategories = ref(false);
const isAddCategoryModal = ref(false);
const dietCount = ref(0);
const tagCount = ref(0);
const transformIcon = ref({
  transform: "rotate(225deg)",
  top: "3px",
});
const Orignalname = ref("");
const OrignalDietname = ref("");
const isValidInputlink = ref(true);
const isDisplayCreateTagModal = ref(false);
const isDisplayCreateDietModal = ref(false);
const isAddTagModal = ref(false);
const isAddDietModal = ref(false);
const isTagGroupDeletePopup = ref(false);
const isDietGroupDeletePopup = ref(false);
const groupName = ref("");
const renameGroupName = ref("");
const renameDefault = ref("");
const formatedTagPopUp = ref([]);
const countTagSelected = ref(0);
const countDietSelected = ref(0);
const tagUpdateList = ref([]);
const addTagTotal = ref(0);
const fromPopUpTag = ref(0);
const sizePopUpTag = ref(9);
const queryTag = ref("");
const queryDiet = ref("");
const isSearchExitOtherEnable = ref(false);
const searchTagISINList = ref([]);
const isinTagList = ref([]);
const fromTag = ref(0);
const sizeTag = ref(9);
const tagList = ref([]);
const formattedFilter = ref([]);
const formattedCategoryPopUp = ref([]);
const countCategoriesSelected = ref(0);
const categoryUpdateList = ref([]);
const addCategoriesTotal = ref(0);
const selectedTag = ref([]);
const selectedDiet = ref([]);
const isNonGroup = ref(false);
const isDeleteTagModal = ref(false);
const isDeleteDietModal = ref(false);
const editTagList = ref(0);
const groupTitleName = ref("");
const fromPopUp = ref(0);
const sizePopUp = ref(9);
const searchCategoryISINList = ref([]);
const isinList = ref([]);
const categoriesList = ref([]);
const formattedCategory = ref([]);
const sizeCategories = ref(9);
const selectedCategory = ref([]);
const totalCategoriesList = ref([]);
const isErrorOccuredModal = ref(false);
const isunableToSaveFilter = ref(false);
const isAddButtonValidInput = ref(false);
const isDeleteCategoryModal = ref(false);
const dataIsinTransfer = ref("");
const configData = ref({});
const isCampaignModified = ref(false);
const isNoResultSectionVisible = ref(false);
const isConfirmModalVisible = ref(false);
const queryCategory = ref("");
const isSearchExitEnable = ref(false);
const isRenameTagModal = ref(false);
const isRenameDietModal = ref(false);
const isIndexGroup = ref(0);
const isDisplayNoRecipeSection = ref(false);
const selectGroupTypes = ref([
  { key: "tagGroup", options: "Tag Group" },
  { key: "dietGroup", options: "Diet Group" },
]);
const emptyDietCheck = ref([]);
const emptyTagCheck = ref([]);
const selectedGroupType = ref("tagGroup");
const isDisplayTagMessage = ref(false);
const isShowDeleteTagGroupMessage = ref(false);
const hasPublish = ref(false);
const isEditSearchLoading = ref(false);
const hasNoResultFound = ref(false);
const lang = ref("");
const isAdminCheck = ref(false);
const drag = ref(false);
const isCategoryNameTooltipVisible = ref(false);
const isTagNameTooltipVisible = ref(false);
const isTagDetailNameTooltipVisible = ref(false);
const isTagNameLangTooltipVisible = ref(false);

// Computed properties
const hasLoadMoreItems = computed(() => {
  const totalItemsCount = fromPopUp.value + sizePopUp.value;
  return (
    addCategoriesTotal.value > totalItemsCount &&
    formattedCategoryPopUp.value.length
  );
});

// Lifecycle hooks
onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      lang.value = store.getters["userData/getDefaultLang"];
      triggerLoading("CAMPAIGN_MODIFIED", false);
      triggerLoading("ROUTE_LOADING", false);
      document.addEventListener("keyup", handleESCClickOutside);
      isAdminCheck.value = store.getters["userData/isAdmin"];
      isEditSearchLoading.value = true;
      await getEditSearchAsync();
      await getCategoryListAsync();
      await getRecipeDietsAsync();
      await getTagListModalAsync();
      $eventBus.on("editSearchMain", async () => {
          cancelChangedAsync(true);
        });
      $tracker.sendEvent($keys.EVENT_KEY_NAMES.VIEW_SEARCH_FILTER, {}, { ...LOCAL_TRACKER_CONFIG });
    }
  });
});

onUnmounted(() => {
  document.removeEventListener("keyup", handleESCClickOutside);
});

// Methods
function closeSearch() {
  backButtonConfirm();
  closeModal();
}

function checkIngtagName(tag, index) {
  const name = getRef("tagname" + index);
  if (name.scrollWidth > name.clientWidth) {
    isTagNameTooltipVisible.value = true;
  }
}

function hideTagNameTip(index) {
  const name = getRef("tagname" + index);
  if (name.scrollWidth > name.clientWidth) {
    isTagNameTooltipVisible.value = false;
  }
}

function handleESCClickOutside(event) {
  if (event.key === "Escape") {
    closeModal();
  }
}

function selectGroup(data) {
  selectedGroupType.value = data.key;
}

async function cancelChangedAsync(data) {
  if (data) {
    isEditSearchLoading.value = true;
    root.$emit("routeloading", isEditSearchLoading.value);
  }
  isOpenCategories.value = false;
  isAddCategoryModal.value = false;
  isDisplayCreateTagModal.value = false;
  isDisplayCreateDietModal.value = false;
  isAddTagModal.value = false;
  isAddDietModal.value = false;
  isValidInputlink.value = true;
  isAddButtonValidInput.value = false;
  groupName.value = "";
  renameDefault.value = "";
  renameGroupName.value = "";
  formatedTagPopUp.value = [];
  countTagSelected.value = 0;
  tagUpdateList.value = [];
  addTagTotal.value = 0;
  fromPopUpTag.value = 0;
  sizePopUpTag.value = 9;
  queryTag.value = "";
  dietPopupList.value = [];
  queryDiet.value = "";
  isSearchExitOtherEnable.value = false;
  searchTagISINList.value = [];
  isinTagList.value = [];
  fromTag.value = 0;
  sizeTag.value = 9;
  tagList.value = [];
  formattedFilter.value = [];
  formattedCategoryPopUp.value = [];
  countCategoriesSelected.value = 0;
  categoryUpdateList.value = [];
  addCategoriesTotal.value = 0;
  selectedTag.value = [];
  isNonGroup.value = false;
  isDeleteTagModal.value = false;
  isDeleteDietModal.value = false;
  editTagList.value = 0;
  groupTitleName.value = "";
  fromPopUp.value = 0;
  sizePopUp.value = 9;
  searchCategoryISINList.value = [];
  isinList.value = [];
  categoriesList.value = [];
  formattedCategory.value = [];
  sizeCategories.value = 9;
  selectedCategory.value = [];
  totalCategoriesList.value = [];
  isDeleteCategoryModal.value = false;
  dataIsinTransfer.value = "";
  hasPublish.value = false;
  configData.value = {};
  isCampaignModified.value = false;
  isConfirmModalVisible.value = false;
  queryCategory.value = "";
  isSearchExitEnable.value = false;
  isIndexGroup.value = 0;
  isFilterPublishing.value = false;

  // Asynchronous actions
  await getEditSearchAsync();
  await getCategoryListAsync();
  await getCategoryStatisticsPopUp();
  await getRecipeDietsAsync("");
  await getTagListModalAsync("");
}

function call(index) {
  const catname = getRef(`catFilter${index}`);
  if (
    catname.offsetWidth < catname.scrollWidth ||
    catname.scrollWidth > catname.clientWidth
  ) {
    isCategoryNameTooltipVisible.value = true;
  }
}

function hide(index) {
  const catname = getRef(`catFilter${index}`);
  if (
    catname.offsetWidth < catname.scrollWidth ||
    catname.scrollWidth > catname.clientWidth
  ) {
    isCategoryNameTooltipVisible.value = true;
  }
}

function handleDrag(event) {
  isCategoryNameTooltipVisible.value = false;
  isTagNameTooltipVisible.value = false;
  isTagDetailNameTooltipVisible.value = false;
  isTagNameLangTooltipVisible.value = false;
  if (event?.moved || event?.added) {
    isCampaignModified.value = true;
  }
}

function checkDietName(index, value) {
  const dname = getRef(`diet${value}${index}`);
  if (
    dname.offsetWidth < dname.scrollWidth ||
    dname.scrollWidth > dname.clientWidth
  ) {
    isTagNameLangTooltipVisible.value = true;
  }
}

function hideDietNameTip(index, value) {
  const dname = getRef(`diet${value}${index}`);
  if (
    dname.offsetWidth < dname.scrollWidth ||
    dname.scrollWidth > dname.clientWidth
  ) {
    isTagNameLangTooltipVisible.value = false;
  }
}

function checkFilterTagName(index, value) {
  const checkName = getRef(`checkname${value}${index}`);
  if (
    checkName.scrollWidth > checkName.clientWidth ||
    checkName.offsetWidth < checkName.scrollWidth
  ) {
    isTagDetailNameTooltipVisible.value = true;
  }
}

function hideFilterTagNameTip(index, value) {
  const checkName = getRef("checkname" + value + index);
  if (checkName.scrollWidth > checkName.clientWidth) {
    isTagDetailNameTooltipVisible.value = false;
  }
}

function tagRenamePopup(data, idx, name, type) {
  if (type === "tags") {
    Orignalname.value = name;
    isRenameTagModal.value = true;
    renameDefault.value = data.name ? data.name : "";
    renameGroupName.value = data.name ? data.name : "";
    isIndexGroup.value = idx;
  } else if (type === "diets") {
    isRenameDietModal.value = true;
    OrignalDietname.value = name;
    renameDefault.value = data.name ? data.name : "";
    renameGroupName.value = data.name ? data.name : "";
    isIndexGroup.value = idx;
  }
}

function updateGroupName(originalName, newName, index) {
  if (
    !isAddButtonValidInput.value &&
    newName.trim() &&
    originalName !== newName
  ) {
    formattedFilter.value.forEach((item, idx) => {
      if (item?.name && index === idx) {
        item.name = newName.trim();
        isCampaignModified.value = true;
        closeModal();
      }
    });
  }
}

function groupRenameEnter() {
  updateGroupName(Orignalname.value, renameGroupName.value, isIndexGroup.value);
}

function groupDietRenameEnter() {
  updateGroupName(
    OrignalDietname.value,
    renameGroupName.value,
    isIndexGroup.value
  );
}

function inputRenameChecked(groupType) {
  checkInput(groupType, renameGroupName.value.trim(), renameDefault.value);
}

function inputChecked(groupType) {
  checkInput(groupType, groupName.value.trim());
}

function checkInput(groupType, text, defaultName = "") {
  isAddButtonValidInput.value = false;
  isValidInputlink.value = true;

  if (text) {
    formattedFilter.value.forEach((item) => {
      if (item?.type === groupType && item?.name) {
        const itemNameUpperCase = item.name.toUpperCase();
        const textUpperCase = text.toUpperCase();

        if (
          textUpperCase === itemNameUpperCase &&
          textUpperCase !== defaultName.toUpperCase()
        ) {
          isAddButtonValidInput.value = true;
          isValidInputlink.value = false;
        }
      }
    });
  }
}

function addItemToGroup(groupType, selectedItems) {
  formattedFilter.value.forEach((item, index) => {
    if (
      item?.values &&
      item?.name === groupTitleName.value &&
      item?.type === groupType
    ) {
      formattedFilter.value[index].values = [
        ...selectedItems,
        ...formattedFilter.value[index].values,
      ] ?? [];
    }
  });
  isCampaignModified.value = true;
  closeModal();
}

function addTagToGroup() {
  addItemToGroup(t("SEARCH_FILTER.TAGS"), selectedTag.value);
}

function addDietToGroup() {
  addItemToGroup(t("SEARCH_FILTER.DIETS"), selectedDiet.value);
}

function createTagGroup() {
  const payload = {
    name: groupName.value ? groupName.value : "",
    dropDown: true,
    type: "tags",
    values: selectedTag.value ? selectedTag.value : [],
  };
  formattedFilter.value.unshift(payload);
  isCampaignModified.value = true;
  closeModal();
}

function createDietGroup() {
  const payload = {
    name: groupName.value ? groupName.value : "",
    dropDown: true,
    type: "diets",
    values: selectedDiet.value ? selectedDiet.value : [],
  };
  formattedFilter.value.unshift(payload);
  isCampaignModified.value = true;
  closeModal();
}

function deleteTagGroupPopUp(tag, index) {
  dataIsinTransfer.value = index;
  if (tag.type === "tags") {
    isTagGroupDeletePopup.value = true;
    isShowDeleteTagGroupMessage.value = true;
  } else if (tag.type === "diets") {
    isDietGroupDeletePopup.value = true;
    isShowDeleteTagGroupMessage.value = false;
  }
}

function deleteTagGroupBtn() {
  isCampaignModified.value = true;
  let filterType = "";
  formattedFilter.value.forEach((item, index) => {
    if (item && index === dataIsinTransfer.value) {
      filterType = item.type;
      formattedFilter.value.splice(index, 1);
    }
  });
  closeModal();
  if (filterType.endsWith("s")) {
    filterType = filterType.slice(0, -1);
  }
  triggerLoading($keys.KEY_NAMES.DELETED_SUCCESS, filterType);
}

function deleteCategory(data) {
  isDeleteCategoryModal.value = true;
  dataIsinTransfer.value = data.isin;
}

function groupNameEnter(dataType) {
  if (!isAddButtonValidInput.value && groupName.value.trim()) {
    if (dataType === "tags") {
      groupName.value = groupName.value.trim();
      selectedTag.value = [];
      countTagSelected.value = 0;
      openTagListModalAsync(true, "");
    } else if (dataType === "diets") {
      groupName.value = groupName.value.trim();
      selectedDiet.value = [];
      countDietSelected.value = 0;
      openDietListModalAsync(true, "");
    }
  }
}

async function backGroupModalAsync(dataType) {
  if (dataType === "tags") {
    isAddTagModal.value = false;
    isDisplayCreateTagModal.value = true;
    queryTag.value = null;
    selectedTag.value.forEach((tag) => {
      formatedTagPopUp.value.map((data) => {
        if (tag.isin === data.isin) {
          data.isChecked = false;
        }
      });
    });
    fromPopUpTag.value = 0;
    sizePopUpTag.value = 9;
    addTagTotal.value = 0;
    selectedTag.value = [];
    countTagSelected.value = 0;
    await getTagListModalAsync(groupName.value.trim());
  } else if (dataType === "diets") {
    isAddDietModal.value = false;
    isDisplayCreateDietModal.value = true;
    queryDiet.value = null;
    countDietSelected.value = 0;
    selectedDiet.value = [];
    await getRecipeDietsAsync(groupName.value.trim());
  }
}

function backToSelectType(dataType) {
  if (dataType === "tags") {
    isDisplayCreateTagModal.value = false;
    isGroupModalVisible.value = true;
    groupName.value = "";
    formatedTagPopUp.value = [];
    editTagList.value = 0;
    selectedTag.value = [];
    countTagSelected.value = 0;
  } else if (dataType === "diets") {
    isDisplayCreateDietModal.value = false;
    isGroupModalVisible.value = true;
    groupName.value = "";
    dietPopupList.value = [];
    editTagList.value = 0;
    selectedDiet.value = [];
    countDietSelected.value = 0;
  }
}

function tagDropdown(tag) {
  let tempArray = [];
  formattedFilter.value?.map((item) => {
    if (tag.type === item.type && tag.name === item.name) {
      item.dropDown = !item.dropDown;
    }
  });
  tempArray = formattedFilter.value;
  formattedFilter.value = [];
  formattedFilter.value = tempArray;
}

function displayCreateTagModal() {
  isGroupModalVisible.value = true;
}

async function openDietListModalAsync(data, name) {
  if (data) {
    isAddDietModal.value = true;
    isDisplayCreateDietModal.value = false;
    if (editTagList.value === 0) {
      editTagList.value++;
      dietPopupList.value = [];
      countDietSelected.value = 0;
      isNonGroup.value = false;
      await getRecipeDietsAsync(groupName.value.trim());
    }
  } else {
    isNonGroup.value = true;
    isTableDataLoading.value = true;
    groupTitleName.value = name;
    isAddDietModal.value = true;
    dietPopupList.value = [];
    countDietSelected.value = 0;
    await getRecipeDietsAsync(groupTitleName.value.trim());
  }
}

async function openTagListModalAsync(data, name) {
  if (data) {
    isAddTagModal.value = true;
    isDisplayCreateTagModal.value = false;
    if (editTagList.value === 0) {
      editTagList.value++;
      fromPopUpTag.value = 0;
      formatedTagPopUp.value = [];
      tagUpdateList.value = [];
      countTagSelected.value = 0;
      addTagTotal.value = 0;
      isNonGroup.value = false;
      await getTagListModalAsync(groupName.value.trim());
    }
  } else {
    isNonGroup.value = true;
    isTableDataLoading.value = true;
    groupTitleName.value = name;
    isAddTagModal.value = true;
    fromPopUpTag.value = 0;
    formatedTagPopUp.value = [];
    tagUpdateList.value = [];
    countTagSelected.value = 0;
    addTagTotal.value = 0;
    await getTagListModalAsync(groupTitleName.value.trim());
  }
}

async function getTagListModalAsync(dataName) {
  const payload = {
    type: "tag",
    country: lang.value.split("-")[1],
    lang: lang.value.split("-")[0],
    q: queryTag.value ? queryTag.value.toLowerCase() : "",
    from: fromPopUpTag.value,
    size: sizePopUpTag.value,
    sort: "lastMod",
    status: "active",
  };

  try {
    await store.dispatch("categories/getMasterListDataAsync", { payload });
    const response = await store.getters["categories/getMasterListData"];
    if (dataName !== "") {
      processResponseData(response, dataName);
      finalizeTagList(response);
    } else {
      emptyTagCheck.value = response?.results || [];
    }
    return response.results ?? [];
  } catch {
    showLoader.value = false;
    isDisplayNoRecipeSection.value = true;
    return [];
  }
}

function processResponseData(response, dataName) {
  response.results.forEach((data) => {
    data.isChecked = false;
    data.isAlreadyAddedTag = false;
  });

  selectedTag.value.forEach((tag) => {
    response.results.forEach((data) => {
      if (tag.isin === data.isin) {
        data.isChecked = true;
      }
    });
  });
  response.results.forEach((data) => {
    formattedFilter.value.forEach((item) => {
      if (
        item &&
        item.values &&
        item.values.length > 0 &&
        item.name === dataName
      ) {
        item.values.forEach((itemdata) => {
          if (data.isin === itemdata.isin) {
            data.isAlreadyAddedTag = true;
          }
        });
      }
    });
  });
}
function finalizeTagList(response) {
  isSearchExitOtherEnable.value = queryTag.value !== "";
  addTagTotal.value = response.total;
  isTableDataLoading.value = false;
  if (!response.results || response.results.length === 0) {
    isDisplayNoRecipeSection.value = true;
    return;
  }
  tagUpdateList.value = [...tagUpdateList.value, ...response.results];
  if (tagCount.value === 0) {
    tagCount.value = response.total;
  }
  if (tagUpdateList.value.length > 0) {
    getTagStatisticsPopUpAsync();
  }
}

async function getRecipeDietsAsync(dataName) {
  try {
    await store.dispatch("recipe/getRecipeDietsAsync", {
      query: queryDiet.value ? queryDiet.value.toLowerCase() : "",
      recipeCount: true,
      lang: lang.value,
    });
    const response = store.getters["recipe/getRecipeDiets"];

    if (dataName !== "") {
      response.results.forEach((data) => {
        data.isChecked = false;
        data.isAlreadyAddedDiet = false;
      });

      selectedDiet.value.forEach((diet) => {
        const matchedData = response.results.find(
          (data) => diet.id === data.id
        );
        if (matchedData) matchedData.isChecked = true;
      });

      response.results.forEach((data) => {
        formattedFilter.value.forEach((item) => {
          if (item?.values?.length && item.name === dataName) {
            const matchedItem = item.values.find(
              (itemData) => Number(itemData.id) === Number(data.id)
            );
            if (matchedItem) data.isAlreadyAddedDiet = true;
          }
        });
      });

      dietPopupList.value = response?.results ?? [];
      dietCount.value = dietCount.value || response?.total || 0;
      isDisplayNoRecipeSection.value = dietPopupList.value.length === 0;
      isSearchExitOtherEnable.value = queryDiet.value !== "";
      isTableDataLoading.value = false;
    } else {
      emptyDietCheck.value = response?.results ?? [];
    }
  } catch {
    isTableDataLoading.value = false;
    isDisplayNoRecipeSection.value = false;
  }
}

async function getTagStatisticsPopUpAsync(loadMoreTagList) {
  const payload = buildTagStatisticsPayload(loadMoreTagList);
  try {
    await store.dispatch("categoriesGroup/getCategoryStatisticsAsync", {
      payload,
    });
    const response = store.getters["categoriesGroup/getCategoryStatistics"];
    processTagStatisticsResponse(response, loadMoreTagList);
  } catch {
    showLoader.value = false;
  }
}

function buildTagStatisticsPayload(loadMoreTagList) {
  const isins = (loadMoreTagList ?? tagUpdateList.value).map((data) => data.isin);
  return {
    type: "tag",
    country: lang.value.split("-")[1],
    lang: lang.value.split("-")[0],
    isins: isins.join(","),
  };
}

function processTagStatisticsResponse(response, loadMoreTagList) {
  const updateList = loadMoreTagList || tagUpdateList.value;

  updateList.forEach((data) => {
    const item = response.find((item) => item.isin === data.isin);
    if (item) {
      data.totalRecipes = item.totalRecipes;
    }
  });

  formatedTagPopUp.value = [...new Set([...tagUpdateList.value, ...updateList])];
}

async function loadUpdateTagPopUpAsync() {
  let loadMoreTagList = [];
  let from = parseInt(fromPopUpTag.value) + sizePopUpTag.value;
  if (from < addTagTotal.value) {
    fromPopUpTag.value = from;

    if (isNonGroup.value) {
      loadMoreTagList = await getTagListModalAsync(groupTitleName.value.trim());
    } else {
      loadMoreTagList = await getTagListModalAsync(groupName.value.trim());
    }
    if (loadMoreTagList && loadMoreTagList.length > 0) {
      await getTagStatisticsPopUpAsync(loadMoreTagList);
      tagUpdateList.value = [...tagUpdateList.value, ...loadMoreTagList];
    }
  }
}

async function resetTagQueryAsync() {
  addTagTotal.value = 0;
  fromPopUpTag.value = 0;
  formatedTagPopUp.value = [];
  tagUpdateList.value = [];
  queryTag.value = "";
  if (isNonGroup.value) {
    await getTagListModalAsync(groupTitleName.value.trim());
  } else {
    await getTagListModalAsync(groupName.value.trim());
  }
  isSearchExitOtherEnable.value = false;
}

async function resetDietQueryAsync() {
  dietPopupList.value = [];
  queryDiet.value = "";
  if (isNonGroup.value) {
    await getRecipeDietsAsync(groupTitleName.value.trim());
  } else {
    await getRecipeDietsAsync(groupName.value.trim());
  }
  isSearchExitOtherEnable.value = false;
}

async function getTagSearchAsync() {
  if (queryTag.value) {
    addTagTotal.value = 0;
    fromPopUpTag.value = 0;
    formatedTagPopUp.value = [];
    tagUpdateList.value = [];
    if (isNonGroup.value) {
      await getTagListModalAsync(groupTitleName.value.trim());
    } else {
      await getTagListModalAsync(groupName.value.trim());
    }
  }
}

async function getDietSearchAsync() {
  if (queryDiet.value) {
    dietPopupList.value = [];
    if (isNonGroup.value) {
      await getRecipeDietsAsync(groupTitleName.value.trim());
    } else {
      await getRecipeDietsAsync(groupName.value.trim());
    }
  }
}
function deleteCategoryBtn() {
  isCampaignModified.value = true;
  let index = 0;
  formattedCategory.value.forEach((data) => {
    if (dataIsinTransfer.value === data.isin) {
      index = formattedCategory.value.indexOf(data);
      formattedCategory.value.splice(index, 1);
    }
  });
  closeModal();
  $eventBus.emit("deleted");
}

function deleteTagDiet(data, groupName, type) {
  if (type === "tags") {
    isDeleteTagModal.value = true;
    isDisplayTagMessage.value = true;
    dataIsinTransfer.value = {
      isin: data.isin || "",
      groupName: groupName || "",
    };
  } else if (type === "diets") {
    isDeleteDietModal.value = true;
    isDisplayTagMessage.value = false;
    dataIsinTransfer.value = {
      id: data.id || "",
      groupName: groupName || "",
    };
  }
}

function deleteTagDietBtn() {
  isCampaignModified.value = true;
  let filterType = "";

  formattedFilter.value.forEach((item) => {
    const isTagFilter = item.type === t("SEARCH_FILTER.TAGS");
    const isDietFilter = item.type === t("SEARCH_FILTER.DIETS");

    item?.values?.forEach((itemData, tagIndex) => {
      const isTagMatch =
        isTagFilter &&
        dataIsinTransfer.value?.isin === itemData.isin &&
        dataIsinTransfer.value?.groupName === item.name;
      const isDietMatch =
        isDietFilter &&
        dataIsinTransfer.value?.id === itemData.id &&
        dataIsinTransfer.value?.groupName === item.name;

      if (isTagMatch || isDietMatch) {
        filterType = item.type;
        item.values.splice(tagIndex, 1);
      }
    });
  });

  filterType = filterType.endsWith("s") ? filterType.slice(0, -1) : filterType;
  closeModal();
  instance.proxy.$eventBus.emit($keys.KEY_NAMES.DELETED, filterType);
}

function savePublishSearch() {
  let errorMsg = false;
  isunableToSaveFilter.value = false;

  if (formattedFilter.value?.length) {
    formattedFilter.value.forEach((item) => {
      if (item?.values?.length === 0) {
        errorMsg = true;
        isunableToSaveFilter.value = true;
      }
    });
  }

  if (!errorMsg) {
    closeModal();
    postEditSearch();
    isFilterPublishing.value = true;
  } else {
    isErrorOccuredModal.value = true;
    hasPublish.value = false;
  }
}

function postEditSearch() {
  if (!configData.value) {
    configData.value = {
      filters: [],
    };
  }

  configData.value.filters = configData.value.filters.filter(
    (filter) =>
      filter.type !== "categories" &&
      filter.type !== "tags" &&
      filter.type !== "diets"
  );

  if (formattedFilter.value?.length) {
    formattedFilter.value.forEach((item) => {
      if (item) {
        const values =
          item.values?.map((value) => {
            if (item.type === t("SEARCH_FILTER.TAGS")) {
              return { isin: value.isin };
            } else if (item.type === t("SEARCH_FILTER.DIETS")) {
              return { id: value.id };
            }
            return null;
          }) || [];

        const payload = {
          name: item.name || "",
          type: item.type || "",
          values: values,
        };

        configData.value.filters.push(payload);
      }
    });
  }

  if (formattedCategory.value.length !== 0) {
    configData.value.filters.unshift({
      name: "Category",
      type: "categories",
      values:
        formattedCategory.value.map((data) => ({ isin: data.isin })) || [],
    });
  }

  store
    .dispatch("editSearch/postEditSearchAsync", {
      payload: configData.value,
      user: $auth?.user?.value?.email,
    })
    .then(async () => {
      triggerLoading($keys.KEY_NAMES.PUBLISH_DATA);
      await cancelChangedAsync();
      $eventBus.emit("campaignModified", false);
    })
    .catch(() => {
      isErrorOccuredModal.value = true;
      isFilterPublishing.value = false;
    });
}

function selectedGroupOption() {
  if (selectedGroupType.value === "tagGroup") {
    isDisplayCreateTagModal.value = true;
    isGroupModalVisible.value = false;
  } else if (selectedGroupType.value === "dietGroup") {
    isDisplayCreateDietModal.value = true;
    isGroupModalVisible.value = false;
  }
}

function publishData() {
  hasPublish.value = true;
}

function backButton() {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backButtonConfirm();
  }
}

function backButtonConfirm() {
  router.push("overview");
}
function closeModal() {
  if (isAddCategoryModal.value) {
    resetQueryAsync();
  }
  selectedGroupType.value = "tagGroup";
  isGroupModalVisible.value = false;
  isIndexGroup.value = 0;
  hasPublish.value = false;
  isDisplayCreateTagModal.value = false;
  isDisplayCreateDietModal.value = false;
  isRenameTagModal.value = false;
  isRenameDietModal.value = false;
  isAddTagModal.value = false;
  isAddDietModal.value = false;
  isTagGroupDeletePopup.value = false;
  isDietGroupDeletePopup.value = false;
  editTagList.value = 0;
  isErrorOccuredModal.value = false;
  isunableToSaveFilter.value = false;
  selectedTag.value = [];
  selectedDiet.value = [];
  isValidInputlink.value = true;
  isAddButtonValidInput.value = false;
  selectedCategory.value = [];
  groupTitleName.value = "";
  groupName.value = "";
  renameDefault.value = "";
  renameGroupName.value = "";
  isDeleteTagModal.value = false;
  isDeleteDietModal.value = false;
  isConfirmModalVisible.value = false;
  isAddCategoryModal.value = false;
  isDeleteCategoryModal.value = false;
  hasPublish.value = false;
  queryCategory.value = "";
  isSearchExitEnable.value = false;
  queryTag.value = "";
  dietPopupList.value = [];
  queryDiet.value = "";
  isSearchExitOtherEnable.value = false;
}

function isChecked(item) {
  if (item.isChecked) {
    item.isChecked = false;
    selectedCategory.value = selectedCategory.value.filter(
      (data) => data.isin !== item.isin
    );
  } else {
    item.isChecked = true;
    selectedCategory.value.push(item);
  }
  countCategoriesSelected.value = selectedCategory.value.length;
}

function isCheckedTag(item) {
  if (item.isChecked) {
    item.isChecked = false;
    selectedTag.value = selectedTag.value.filter(
      (data) => data.isin !== item.isin
    );
  } else {
    item.isChecked = true;
    selectedTag.value.push(item);
  }
  countTagSelected.value = selectedTag.value.length;
}

function isCheckedDiet(item) {
  if (item.isChecked) {
    item.isChecked = false;
    selectedDiet.value = selectedDiet.value.filter(
      (data) => data.id !== item.id
    );
  } else {
    item.isChecked = true;
    selectedDiet.value.push(item);
  }
  countDietSelected.value = selectedDiet.value.length;
}

function addCategories() {
  isCampaignModified.value = true;
  totalCategoriesList.value = [];
  isCampaignModified.value = true;
  if (selectedCategory.value && formattedCategory.value) {
    totalCategoriesList.value = [
      ...selectedCategory.value,
      ...formattedCategory.value,
    ] ?? [];
  }
  formattedCategory.value = totalCategoriesList.value;
  selectedCategory.value = [];
  closeModal();
}

async function addCategoryButtonAsync() {
  fromPopUp.value = 0;
  formattedCategoryPopUp.value = [];
  categoryUpdateList.value = [];
  countCategoriesSelected.value = 0;
  isAddCategoryModal.value = true;
  addCategoriesTotal.value = 0;
  isTableDataLoading.value = true;
  await getCategoryListAsync();
}

async function loadUpdatePopUpAsync() {
  let from = parseInt(fromPopUp.value) + sizePopUp.value;
  if (from < addCategoriesTotal.value) {
    fromPopUp.value = from;
    let loadMoreCategoryList = await getCategoryListAsync();
    if (loadMoreCategoryList && categoryUpdateList.value) {
      await getCategoryStatisticsPopUp(loadMoreCategoryList);
      categoryUpdateList.value = [
        ...categoryUpdateList.value,
        ...loadMoreCategoryList,
      ] ?? [];
    }
  }
}

async function getCategorySearchAsync() {
  if (queryCategory.value) {
    addCategoriesTotal.value = 0;
    fromPopUp.value = 0;
    formattedCategoryPopUp.value = [];
    categoryUpdateList.value = [];
    await getCategoryListAsync();
  }
}

async function getCategoryListAsync() {
  const payload = {
    type: "category",
    country: lang.value.split("-")[1],
    lang: lang.value.split("-")[0],
    q: queryCategory.value,
    from: fromPopUp.value,
    size: sizeCategories.value,
    sort: "lastMod",
    status: "active",
  };

  try {
    await store.dispatch("categories/getMasterListDataAsync", { payload });
    const response = store.getters["categories/getMasterListData"];
    response.results.forEach((data) => {
      data.isChecked = selectedCategory.value.some(
        (category) => category.isin === data.isin
      );
      data.isAlreadyAddedCategory = formattedCategory.value.some(
        (item) => item.isin === data.isin
      );
    });

    if (fromPopUp.value >= 9) {
      return response.results;
    } else {
      categoryUpdateList.value = response.results || [];
      isDisplayNoRecipeSection.value = !categoryUpdateList.value.length;

      if (categoryUpdateList.value.length) {
        getCategoryStatisticsPopUp();
      }
    }

    isSearchExitEnable.value = !!queryCategory.value;
    addCategoriesTotal.value = response.total;
  } catch (error) {
    console.error(error);
    showLoader.value = false;
    isDisplayNoRecipeSection.value = true;
  } finally {
    isTableDataLoading.value = false;
  }
}

const resetQueryAsync = async () => {
  addCategoriesTotal.value = 0;
  fromPopUp.value = 0;
  formattedCategoryPopUp.value = [];
  categoryUpdateList.value = [];
  queryCategory.value = "";
  await getCategoryListAsync();
  isSearchExitEnable.value = false;
};

async function getEditSearchAsync() {
  try {
    await store.dispatch("editSearch/getEditSearchAsync");
    const response = store.getters["editSearch/getEditSearch"];
    if (response) {
      configData.value = response;

      if (response.filters) {
        searchCategoryISINList.value = response.filters.filter(
          (data) => data.type === t("SEARCH_FILTER.CATEGORIES")
        );
        searchTagISINList.value = response.filters.filter(
          (data) =>
            data.type === t("SEARCH_FILTER.TAGS") ||
            data.type === t("SEARCH_FILTER.DIETS")
        );

        tagList.value = formatTagList(searchTagISINList.value);
        categoriesList.value = formatCategoryList(searchCategoryISINList.value);

        collectISINs(searchCategoryISINList.value, isinList.value);
        collectISINs(searchTagISINList.value, isinTagList.value);
      }

      formattedFilter.value = [...tagList.value] || [];
      formattedCategory.value = [...categoriesList.value] || [];
      isEditSearchLoading.value = false;
      hasNoResultFound.value = false;
      triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isEditSearchLoading.value);
    }
  } catch {
    isEditSearchLoading.value = false;
    hasNoResultFound.value = true;
    configData.value = {
      filters: [],
    };
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isEditSearchLoading.value);
  }
}

function formatTagList(tagList) {
  return tagList.map((item) => {
    item.dropDown = false;
    if (item?.values?.length) {
      item.values = item.values.map((itemData) =>
        createPayload(item, itemData)
      );
    }
    return item;
  });
}

function formatCategoryList(categoryList) {
  const categories = categoryList?.[0]?.values ? categoryList[0].values : [];
  return categories.map((itemData) => ({
    isin: itemData.isin,
    totalRecipes: itemData.totalRecipes || 0,
    data: {
      [lang.value]: {
        name: itemData.name || "",
        image: itemData.image || "",
      },
    },
  }));
}

function createPayload(item, itemData) {
  if (item.type === t("SEARCH_FILTER.TAGS")) {
    return {
      isin: itemData.isin || "",
      totalRecipes: itemData.totalRecipes || 0,
      data: {
        [lang.value]: {
          name: itemData.name || "",
        },
      },
    };
  } else if (item.type === t("SEARCH_FILTER.DIETS")) {
    return {
      id: itemData.id || "",
      name: itemData.name || "",
      image: itemData.image || "",
      totalRecipes: itemData.totalRecipes || 0,
    };
  }
}

function collectISINs(list, targetArray) {
  if (list?.length) {
    list.forEach((data) => {
      if (data?.values?.length) {
        data.values.forEach((item) => targetArray.push(item.isin));
      }
    });
  }
}

async function getCategoryStatisticsPopUp(loadMoreCategoryList) {
  const isins = collectCategoryISINs(loadMoreCategoryList);

  const payload = {
    type: "category",
    country: lang.value.split("-")[1],
    lang: lang.value.split("-")[0],
    isins: isins.join(","),
  };

  try {
    await store.dispatch("categoriesGroup/getCategoryStatisticsAsync", { payload });
    const response = store.getters["categoriesGroup/getCategoryStatistics"];
    updateCategoryList(response, loadMoreCategoryList);
  } catch {
    showLoader.value = false;
  }
}

function collectCategoryISINs(loadMoreCategoryList) {
  const isins = [];
  const list = loadMoreCategoryList || categoryUpdateList.value;
  list.forEach((data) => {
    isins.push(data.isin);
  });
  return isins;
}

function updateCategoryList(response, loadMoreCategoryList) {
  const updateTotalRecipes = (list) => {
    list.forEach((data) => {
      const item = response.find((item) => item.isin === data.isin);
      if (item) {
        data.totalRecipes = item.totalRecipes;
      }
    });
  };

  updateTotalRecipes(categoryUpdateList.value);

  if (loadMoreCategoryList) {
    updateTotalRecipes(loadMoreCategoryList);
    formattedCategoryPopUp.value = [
      ...categoryUpdateList.value,
      ...loadMoreCategoryList,
    ] ?? [];
  } else {
    formattedCategoryPopUp.value = [...categoryUpdateList.value] ?? [];
  }
}

watchEffect(() => {
  $eventBus.emit($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
});
</script>
